# Deployment Guide

This guide covers various deployment options for the Advanced Notes Application.

## 🚀 Quick Deployment Options

### 1. Netlify (Recommended)

**Step 1: Build the project**
```bash
npm run build
```

**Step 2: Deploy to Netlify**
- Go to [Netlify](https://netlify.com)
- Drag and drop the `build` folder
- Your app is live!

**For continuous deployment:**
1. Connect your GitHub repository
2. Set build command: `npm run build`
3. Set publish directory: `build`
4. Add environment variables in Netlify dashboard

### 2. Vercel

**Step 1: Install Vercel CLI**
```bash
npm install -g vercel
```

**Step 2: Deploy**
```bash
vercel --prod
```

**For GitHub integration:**
1. Connect repository to Vercel
2. Auto-deploys on every push to main branch

### 3. GitHub Pages

**Step 1: Install gh-pages**
```bash
npm install --save-dev gh-pages
```

**Step 2: Add to package.json**
```json
{
  "homepage": "https://yourusername.github.io/repository-name",
  "scripts": {
    "predeploy": "npm run build",
    "deploy": "gh-pages -d build"
  }
}
```

**Step 3: Deploy**
```bash
npm run deploy
```

### 4. Firebase Hosting

**Step 1: Install Firebase CLI**
```bash
npm install -g firebase-tools
```

**Step 2: Initialize Firebase**
```bash
firebase login
firebase init hosting
```

**Step 3: Build and Deploy**
```bash
npm run build
firebase deploy
```

## 🔧 Environment Configuration

### Production Environment Variables

Create a `.env.production` file:
```env
REACT_APP_GROQ_API_KEY=your_production_api_key
REACT_APP_NAME=Advanced Notes App
REACT_APP_VERSION=1.0.0
REACT_APP_ENABLE_AI_FEATURES=true
REACT_APP_DEBUG_MODE=false
```

### Platform-Specific Settings

#### Netlify
Add environment variables in:
Site Settings → Environment Variables

#### Vercel
Add environment variables in:
Project Settings → Environment Variables

#### Firebase
Use Firebase Functions for environment variables:
```javascript
const functions = require('firebase-functions');
const config = functions.config();
```

## 📊 Performance Optimization

### Build Optimization

**1. Analyze Bundle Size**
```bash
npm install --save-dev webpack-bundle-analyzer
npm run build
npx webpack-bundle-analyzer build/static/js/*.js
```

**2. Enable Compression**
Most hosting platforms automatically enable gzip compression.

**3. Service Worker**
The app includes a service worker for caching. Enable it in production:
```javascript
// In src/index.js
import { register } from './serviceWorker';
register();
```

### CDN Configuration

**Cloudflare Settings:**
- Enable Auto Minify (CSS, JS, HTML)
- Enable Brotli compression
- Set Browser Cache TTL to 1 month for static assets

**AWS CloudFront:**
- Set TTL for static assets: 31536000 seconds (1 year)
- Enable compression
- Use HTTP/2

## 🔒 Security Considerations

### HTTPS
Always deploy with HTTPS enabled. Most modern hosting platforms provide this automatically.

### Content Security Policy
Add CSP headers for enhanced security:
```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:;
```

### Environment Variables
- Never commit `.env` files to version control
- Use platform-specific environment variable management
- Rotate API keys regularly

## 📱 Mobile App Deployment (PWA)

### Progressive Web App Setup

**1. Update manifest.json**
```json
{
  "name": "Advanced Notes App",
  "short_name": "Notes",
  "description": "Advanced note-taking application",
  "start_url": "/",
  "display": "standalone",
  "theme_color": "#007bff",
  "background_color": "#ffffff",
  "icons": [
    {
      "src": "icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "icon-512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

**2. Service Worker Registration**
```javascript
// Register service worker for offline functionality
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.register('/sw.js');
}
```

## 🧪 Testing in Production

### Pre-deployment Checklist
- [ ] All tests pass: `npm test`
- [ ] Build succeeds: `npm run build`
- [ ] No console errors in production build
- [ ] All features work without API keys (fallback mode)
- [ ] Responsive design tested on multiple devices
- [ ] Performance audit with Lighthouse (score > 90)
- [ ] Accessibility audit passes
- [ ] Cross-browser testing completed

### Post-deployment Verification
- [ ] App loads correctly
- [ ] All features functional
- [ ] No 404 errors for routes
- [ ] HTTPS certificate valid
- [ ] Performance metrics acceptable
- [ ] Error tracking configured

## 📈 Monitoring and Analytics

### Error Tracking
Integrate with services like:
- Sentry
- LogRocket
- Bugsnag

### Analytics
Add analytics tracking:
- Google Analytics
- Mixpanel
- Amplitude

### Performance Monitoring
- Web Vitals tracking
- Real User Monitoring (RUM)
- Synthetic monitoring

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm test -- --coverage --watchAll=false
      
    - name: Build
      run: npm run build
      
    - name: Deploy to Netlify
      uses: nwtgck/actions-netlify@v1.2
      with:
        publish-dir: './build'
        production-branch: main
      env:
        NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
        NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
```

## 🆘 Troubleshooting

### Common Issues

**Build Fails:**
- Check Node.js version compatibility
- Clear node_modules and reinstall: `rm -rf node_modules && npm install`
- Check for syntax errors in code

**App Doesn't Load:**
- Verify build output in `build` folder
- Check browser console for errors
- Ensure all assets are properly referenced

**Environment Variables Not Working:**
- Verify variable names start with `REACT_APP_`
- Check platform-specific environment variable setup
- Restart development server after adding variables

**Performance Issues:**
- Analyze bundle size
- Implement code splitting
- Optimize images and assets
- Enable compression

### Support
For deployment issues:
1. Check platform-specific documentation
2. Review build logs for errors
3. Test locally with production build: `npm run build && npx serve -s build`
4. Contact platform support if needed

---

**Happy Deploying! 🚀**
