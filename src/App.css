/* src/App.css */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f8f9fa;
  overflow: hidden;
}

.app-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus,
[contenteditable]:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Button reset */
button {
  font-family: inherit;
  font-size: inherit;
}

/* Selection styles */
::selection {
  background: rgba(0, 123, 255, 0.2);
}

::-moz-selection {
  background: rgba(0, 123, 255, 0.2);
}

/* Responsive design */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
    height: 100vh;
  }

  body {
    overflow: auto;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background: #1a1a1a;
    color: #e9ecef;
  }

  ::-webkit-scrollbar-track {
    background: #2d2d2d;
  }

  ::-webkit-scrollbar-thumb {
    background: #6c757d;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
  }

  * {
    scrollbar-color: #6c757d #2d2d2d;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  button:focus,
  input:focus,
  select:focus,
  textarea:focus,
  [contenteditable]:focus {
    outline: 3px solid currentColor;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .app-container {
    height: auto;
    overflow: visible;
  }

  body {
    background: white;
    color: black;
    overflow: visible;
  }
}
