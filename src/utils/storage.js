// src/utils/storage.js
import CryptoJS from 'crypto-js';

const NOTES_KEY = 'notes-app-data';
const SETTINGS_KEY = 'notes-app-settings';

// Generate unique ID for notes
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Load notes from localStorage
export const loadNotes = () => {
  try {
    const data = localStorage.getItem(NOTES_KEY);
    if (!data) return [];
    
    const notes = JSON.parse(data);
    return Array.isArray(notes) ? notes : [];
  } catch (error) {
    console.error('Error loading notes:', error);
    return [];
  }
};

// Save notes to localStorage
export const saveNotes = (notes) => {
  try {
    localStorage.setItem(NOTES_KEY, JSON.stringify(notes));
    return true;
  } catch (error) {
    console.error('Error saving notes:', error);
    return false;
  }
};

// Default settings
const getDefaultSettings = () => ({
  theme: 'light',
  fontSize: 14,
  autoSave: true,
  glossaryEnabled: true,
  grammarCheckEnabled: true,
  aiInsightsEnabled: true
});

// Load app settings
export const loadSettings = () => {
  try {
    const data = localStorage.getItem(SETTINGS_KEY);
    if (!data) return getDefaultSettings();

    return { ...getDefaultSettings(), ...JSON.parse(data) };
  } catch (error) {
    console.error('Error loading settings:', error);
    return getDefaultSettings();
  }
};

// Save app settings
export const saveSettings = (settings) => {
  try {
    localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings));
    return true;
  } catch (error) {
    console.error('Error saving settings:', error);
    return false;
  }
};



// Encrypt note content
export const encryptNote = (content, password) => {
  try {
    return CryptoJS.AES.encrypt(content, password).toString();
  } catch (error) {
    console.error('Error encrypting note:', error);
    return null;
  }
};

// Decrypt note content
export const decryptNote = (encryptedContent, password) => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedContent, password);
    return bytes.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('Error decrypting note:', error);
    return null;
  }
};

// Create new note structure
export const createNote = (title = 'Untitled Note', content = '') => ({
  id: generateId(),
  title,
  content,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  pinned: false,
  encrypted: false,
  tags: [],
  wordCount: content.split(/\s+/).filter(word => word.length > 0).length
});

// Update note structure
export const updateNote = (note, updates) => ({
  ...note,
  ...updates,
  updatedAt: new Date().toISOString(),
  wordCount: updates.content ? 
    updates.content.split(/\s+/).filter(word => word.length > 0).length : 
    note.wordCount
});

// Export notes as JSON
export const exportNotes = (notes) => {
  try {
    const exportData = {
      notes,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };
    
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `notes-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    return true;
  } catch (error) {
    console.error('Error exporting notes:', error);
    return false;
  }
};

// Import notes from JSON
export const importNotes = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target.result);
        
        if (data.notes && Array.isArray(data.notes)) {
          resolve(data.notes);
        } else {
          reject(new Error('Invalid file format'));
        }
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = () => reject(new Error('Error reading file'));
    reader.readAsText(file);
  });
};

// Clear all data (with confirmation)
export const clearAllData = () => {
  try {
    localStorage.removeItem(NOTES_KEY);
    localStorage.removeItem(SETTINGS_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing data:', error);
    return false;
  }
};

// Get storage usage info
export const getStorageInfo = () => {
  try {
    const notesData = localStorage.getItem(NOTES_KEY) || '';
    const settingsData = localStorage.getItem(SETTINGS_KEY) || '';
    
    return {
      notesSize: new Blob([notesData]).size,
      settingsSize: new Blob([settingsData]).size,
      totalSize: new Blob([notesData + settingsData]).size,
      available: true
    };
  } catch (error) {
    return {
      notesSize: 0,
      settingsSize: 0,
      totalSize: 0,
      available: false,
      error: error.message
    };
  }
};
