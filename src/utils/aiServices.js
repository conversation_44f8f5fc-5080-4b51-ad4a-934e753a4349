// src/utils/aiServices.js

// Mock AI services for development - replace with actual API calls
const GROQ_API_KEY = process.env.REACT_APP_GROQ_API_KEY;
const GROQ_API_URL = 'https://api.groq.com/openai/v1/chat/completions';

// Glossary terms database (can be expanded)
const GLOSSARY_TERMS = {
  'artificial intelligence': 'The simulation of human intelligence in machines that are programmed to think and learn like humans.',
  'machine learning': 'A subset of AI that enables computers to learn and improve from experience without being explicitly programmed.',
  'neural network': 'A computing system inspired by biological neural networks that constitute animal brains.',
  'algorithm': 'A set of rules or instructions given to a computer to help it learn on its own.',
  'data science': 'An interdisciplinary field that uses scientific methods, processes, algorithms and systems to extract knowledge from data.',
  'blockchain': 'A distributed ledger technology that maintains a continuously growing list of records, called blocks.',
  'cryptocurrency': 'A digital or virtual currency that uses cryptography for security.',
  'api': 'Application Programming Interface - a set of protocols and tools for building software applications.',
  'database': 'An organized collection of structured information, or data, typically stored electronically.',
  'frontend': 'The part of a website or application that users interact with directly.',
  'backend': 'The server-side of an application that handles data storage, security, and other server functions.',
  'responsive design': 'An approach to web design that makes web pages render well on a variety of devices and window sizes.',
  'user experience': 'The overall experience of a person using a product, especially in terms of how easy or pleasing it is to use.',
  'user interface': 'The space where interactions between humans and machines occur.',
  'agile': 'A project management and software development methodology that emphasizes flexibility and collaboration.'
};

// Grammar rules for basic checking
const GRAMMAR_RULES = [
  {
    pattern: /\b(there|their|they're)\b/gi,
    check: (match, context) => {
      // Simple context-based suggestions
      if (context.includes('going') || context.includes('are')) return 'they\'re';
      if (context.includes('house') || context.includes('car') || context.includes('book')) return 'their';
      return 'there';
    },
    message: 'Check if you meant "there", "their", or "they\'re"'
  },
  {
    pattern: /\b(your|you're)\b/gi,
    check: (match, context) => {
      if (context.includes('are') || context.includes('going')) return 'you\'re';
      return 'your';
    },
    message: 'Check if you meant "your" or "you\'re"'
  },
  {
    pattern: /\b(its|it's)\b/gi,
    check: (match, context) => {
      if (context.includes('is') || context.includes('has')) return 'it\'s';
      return 'its';
    },
    message: 'Check if you meant "its" or "it\'s"'
  },
  {
    pattern: /\.\s*[a-z]/g,
    message: 'Consider capitalizing the first letter after a period'
  },
  {
    pattern: /\s{2,}/g,
    message: 'Multiple spaces detected'
  }
];

// Identify glossary terms in text
export const identifyGlossaryTerms = (text) => {
  const terms = [];
  const lowerText = text.toLowerCase();
  
  Object.keys(GLOSSARY_TERMS).forEach(term => {
    const regex = new RegExp(`\\b${term}\\b`, 'gi');
    let match;
    
    while ((match = regex.exec(text)) !== null) {
      terms.push({
        term: match[0],
        definition: GLOSSARY_TERMS[term.toLowerCase()],
        start: match.index,
        end: match.index + match[0].length,
        originalTerm: term
      });
    }
  });
  
  return terms.sort((a, b) => a.start - b.start);
};

// Get definition for a specific term
export const getTermDefinition = (term) => {
  return GLOSSARY_TERMS[term.toLowerCase()] || null;
};

// Basic grammar checking
export const checkGrammar = (text) => {
  const issues = [];
  
  GRAMMAR_RULES.forEach(rule => {
    let match;
    while ((match = rule.pattern.exec(text)) !== null) {
      const context = text.substring(
        Math.max(0, match.index - 20),
        Math.min(text.length, match.index + match[0].length + 20)
      );
      
      let suggestion = null;
      if (rule.check) {
        suggestion = rule.check(match[0], context);
      }
      
      issues.push({
        text: match[0],
        start: match.index,
        end: match.index + match[0].length,
        message: rule.message,
        suggestion,
        type: 'grammar'
      });
    }
    
    // Reset regex lastIndex for global patterns
    rule.pattern.lastIndex = 0;
  });
  
  return issues;
};

// Generate AI insights (mock implementation)
export const generateInsights = async (content) => {
  // Mock insights - replace with actual AI API call
  const insights = [];
  
  const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
  const avgWordsPerSentence = sentences > 0 ? Math.round(wordCount / sentences) : 0;
  
  // Basic readability insights
  if (wordCount > 500) {
    insights.push({
      type: 'length',
      title: 'Long Content',
      message: 'This note is quite lengthy. Consider breaking it into smaller sections.',
      priority: 'medium'
    });
  }
  
  if (avgWordsPerSentence > 20) {
    insights.push({
      type: 'readability',
      title: 'Complex Sentences',
      message: 'Some sentences are quite long. Consider breaking them down for better readability.',
      priority: 'low'
    });
  }
  
  // Keyword extraction (simple)
  const words = content.toLowerCase().match(/\b\w+\b/g) || [];
  const wordFreq = {};
  words.forEach(word => {
    if (word.length > 3) {
      wordFreq[word] = (wordFreq[word] || 0) + 1;
    }
  });
  
  const topWords = Object.entries(wordFreq)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)
    .map(([word]) => word);
  
  if (topWords.length > 0) {
    insights.push({
      type: 'keywords',
      title: 'Key Topics',
      message: `Main topics appear to be: ${topWords.join(', ')}`,
      priority: 'info'
    });
  }
  
  return insights;
};

// Generate summary (mock implementation)
export const generateSummary = async (content) => {
  if (!content || content.length < 100) {
    return 'Content too short to summarize.';
  }
  
  // Simple extractive summary - take first and last sentences
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
  
  if (sentences.length <= 2) {
    return content;
  }
  
  const firstSentence = sentences[0].trim() + '.';
  const lastSentence = sentences[sentences.length - 1].trim() + '.';
  
  return `${firstSentence} ... ${lastSentence}`;
};

// Suggest related notes (mock implementation)
export const suggestRelatedNotes = (currentNote, allNotes) => {
  if (!currentNote || !allNotes || allNotes.length <= 1) {
    return [];
  }
  
  const currentWords = new Set(
    currentNote.content.toLowerCase().match(/\b\w+\b/g) || []
  );
  
  const relatedNotes = allNotes
    .filter(note => note.id !== currentNote.id)
    .map(note => {
      const noteWords = new Set(
        note.content.toLowerCase().match(/\b\w+\b/g) || []
      );
      
      const intersection = new Set([...currentWords].filter(x => noteWords.has(x)));
      const similarity = intersection.size / Math.max(currentWords.size, noteWords.size);
      
      return { note, similarity };
    })
    .filter(({ similarity }) => similarity > 0.1)
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, 3)
    .map(({ note }) => note);
  
  return relatedNotes;
};

// Real AI API call (when API key is available)
export const callGroqAPI = async (prompt, maxTokens = 150) => {
  if (!GROQ_API_KEY) {
    console.warn('Groq API key not found. Using mock responses.');
    return null;
  }
  
  try {
    const response = await fetch(GROQ_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${GROQ_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'mixtral-8x7b-32768',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: maxTokens,
        temperature: 0.7
      })
    });
    
    if (!response.ok) {
      throw new Error(`API call failed: ${response.status}`);
    }
    
    const data = await response.json();
    return (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) || null;
  } catch (error) {
    console.error('Error calling Groq API:', error);
    return null;
  }
};

// Enhanced AI insights with real API
export const generateAIInsights = async (content) => {
  const prompt = `Analyze this text and provide insights about its content, structure, and key themes. Keep the response concise and actionable:\n\n${content}`;
  
  const aiResponse = await callGroqAPI(prompt);
  
  if (aiResponse) {
    return [{
      type: 'ai-analysis',
      title: 'AI Analysis',
      message: aiResponse,
      priority: 'high'
    }];
  }
  
  // Fallback to mock insights
  return generateInsights(content);
};

// Enhanced summary with real AI
export const generateAISummary = async (content) => {
  const prompt = `Summarize the following text in 2-3 sentences, capturing the main points:\n\n${content}`;
  
  const aiResponse = await callGroqAPI(prompt);
  
  if (aiResponse) {
    return aiResponse;
  }
  
  // Fallback to mock summary
  return generateSummary(content);
};
