// src/App.js
import React, { useState, useEffect } from "react";
import NotesList from "./components/NotesList/NotesList";
import Editor from "./components/Editor/Editor";
import { loadNotes, saveNotes } from "./utils/storage";
import "./App.css";

export default function App() {
  const [notes, setNotes] = useState([]);
  const [selectedId, setSelectedId] = useState(null);

  useEffect(() => {
    setNotes(loadNotes());
  }, []);

  useEffect(() => {
    saveNotes(notes);
  }, [notes]);

  const handleSelect = (id) => setSelectedId(id);

  const handleSave = (note) => {
    setNotes((prev) => {
      const exists = prev.find((n) => n.id === note.id);
      if (exists) {
        return prev.map((n) => (n.id === note.id ? note : n));
      }
      return [note, ...prev];
    });
    setSelectedId(note.id);
  };

  const handleDelete = (id) => {
    setNotes((prev) => prev.filter((n) => n.id !== id));
    setSelectedId(null);
  };

  const handlePin = (id) => {
    setNotes((prev) =>
      prev.map((n) =>
        n.id === id ? { ...n, pinned: !n.pinned } : n
      )
    );
  };

  const selectedNote = notes.find((n) => n.id === selectedId);

  return (
    <div className="app-container">
      <NotesList
        notes={notes}
        onSelect={handleSelect}
        onDelete={handleDelete}
        onPin={handlePin}
        onSave={handleSave}
        selectedId={selectedId}
      />
      <Editor
        note={selectedNote}
        onSave={handleSave}
      />
    </div>
  );
}