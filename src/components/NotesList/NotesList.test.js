// src/components/NotesList/NotesList.test.js
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import NotesList from './NotesList';

// Mock data for testing
const mockNotes = [
  {
    id: '1',
    title: 'Test Note 1',
    content: 'This is the content of test note 1',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    pinned: false,
    encrypted: false,
    wordCount: 8
  },
  {
    id: '2',
    title: 'Pinned Note',
    content: 'This is a pinned note',
    createdAt: '2024-01-02T00:00:00.000Z',
    updatedAt: '2024-01-02T00:00:00.000Z',
    pinned: true,
    encrypted: false,
    wordCount: 5
  },
  {
    id: '3',
    title: 'Encrypted Note',
    content: 'encrypted_content_here',
    createdAt: '2024-01-03T00:00:00.000Z',
    updatedAt: '2024-01-03T00:00:00.000Z',
    pinned: false,
    encrypted: true,
    wordCount: 3
  }
];

// Mock functions
const mockProps = {
  notes: mockNotes,
  onSelect: jest.fn(),
  onDelete: jest.fn(),
  onPin: jest.fn(),
  onSave: jest.fn(),
  selectedId: null
};

describe('NotesList Component', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  test('renders notes list with correct title', () => {
    render(<NotesList {...mockProps} />);
    
    expect(screen.getByText('Notes')).toBeInTheDocument();
  });

  test('displays all notes', () => {
    render(<NotesList {...mockProps} />);
    
    expect(screen.getByText('Test Note 1')).toBeInTheDocument();
    expect(screen.getByText('Pinned Note')).toBeInTheDocument();
    expect(screen.getByText('Encrypted Note')).toBeInTheDocument();
  });

  test('shows correct notes count', () => {
    render(<NotesList {...mockProps} />);
    
    expect(screen.getByText('3 notes')).toBeInTheDocument();
  });

  test('displays pinned notes first', () => {
    render(<NotesList {...mockProps} />);
    
    const noteItems = screen.getAllByText(/Note/);
    // Pinned note should appear first
    expect(noteItems[0]).toHaveTextContent('Pinned Note');
  });

  test('shows encrypted icon for encrypted notes', () => {
    render(<NotesList {...mockProps} />);
    
    // Look for lock icon (encrypted indicator)
    const encryptedIcons = document.querySelectorAll('.encrypted-icon');
    expect(encryptedIcons).toHaveLength(1);
  });

  test('calls onSelect when note is clicked', () => {
    render(<NotesList {...mockProps} />);
    
    const firstNote = screen.getByText('Test Note 1');
    fireEvent.click(firstNote.closest('.note-item'));
    
    expect(mockProps.onSelect).toHaveBeenCalledWith('1');
  });

  test('calls onPin when pin button is clicked', () => {
    render(<NotesList {...mockProps} />);
    
    const pinButtons = document.querySelectorAll('.pin-btn');
    fireEvent.click(pinButtons[0]);
    
    expect(mockProps.onPin).toHaveBeenCalled();
  });

  test('shows delete confirmation when delete button is clicked', () => {
    // Mock window.confirm
    window.confirm = jest.fn(() => true);
    
    render(<NotesList {...mockProps} />);
    
    const deleteButtons = document.querySelectorAll('.delete-btn');
    fireEvent.click(deleteButtons[0]);
    
    expect(window.confirm).toHaveBeenCalledWith('Are you sure you want to delete this note?');
    expect(mockProps.onDelete).toHaveBeenCalled();
  });

  test('filters notes by search term', () => {
    render(<NotesList {...mockProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search notes...');
    fireEvent.change(searchInput, { target: { value: 'Pinned' } });
    
    expect(screen.getByText('Pinned Note')).toBeInTheDocument();
    expect(screen.queryByText('Test Note 1')).not.toBeInTheDocument();
  });

  test('shows empty state when no notes match search', () => {
    render(<NotesList {...mockProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search notes...');
    fireEvent.change(searchInput, { target: { value: 'nonexistent' } });
    
    expect(screen.getByText('No notes match your search criteria.')).toBeInTheDocument();
  });

  test('shows empty state when no notes exist', () => {
    const emptyProps = { ...mockProps, notes: [] };
    render(<NotesList {...emptyProps} />);
    
    expect(screen.getByText('No notes yet.')).toBeInTheDocument();
    expect(screen.getByText('Create your first note')).toBeInTheDocument();
  });

  test('highlights selected note', () => {
    const propsWithSelection = { ...mockProps, selectedId: '1' };
    render(<NotesList {...propsWithSelection} />);
    
    const selectedNote = screen.getByText('Test Note 1').closest('.note-item');
    expect(selectedNote).toHaveClass('selected');
  });

  test('sorts notes by different criteria', () => {
    render(<NotesList {...mockProps} />);
    
    const sortSelect = screen.getByDisplayValue('Last Modified');
    fireEvent.change(sortSelect, { target: { value: 'title' } });
    
    // After sorting by title, notes should be in alphabetical order
    const noteItems = screen.getAllByText(/Note/);
    expect(noteItems[0]).toHaveTextContent('Encrypted Note');
    expect(noteItems[1]).toHaveTextContent('Pinned Note');
    expect(noteItems[2]).toHaveTextContent('Test Note 1');
  });

  test('filters notes by type', () => {
    render(<NotesList {...mockProps} />);
    
    const filterSelect = screen.getByDisplayValue('All Notes');
    fireEvent.change(filterSelect, { target: { value: 'pinned' } });
    
    expect(screen.getByText('Pinned Note')).toBeInTheDocument();
    expect(screen.queryByText('Test Note 1')).not.toBeInTheDocument();
    expect(screen.getByText('1 note')).toBeInTheDocument();
  });

  test('creates new note when create button is clicked', () => {
    render(<NotesList {...mockProps} />);
    
    const createButton = screen.getByTitle('Create new note');
    fireEvent.click(createButton);
    
    expect(mockProps.onSave).toHaveBeenCalled();
    expect(mockProps.onSelect).toHaveBeenCalled();
  });

  test('handles export functionality', () => {
    // Mock URL.createObjectURL and related functions
    global.URL.createObjectURL = jest.fn(() => 'mock-url');
    global.URL.revokeObjectURL = jest.fn();
    
    // Mock document.createElement and appendChild
    const mockLink = {
      href: '',
      download: '',
      click: jest.fn()
    };
    document.createElement = jest.fn(() => mockLink);
    document.body.appendChild = jest.fn();
    document.body.removeChild = jest.fn();
    
    render(<NotesList {...mockProps} />);
    
    const exportButton = screen.getByTitle('Export notes');
    fireEvent.click(exportButton);
    
    expect(mockLink.click).toHaveBeenCalled();
  });
});

// Integration test for the complete workflow
describe('NotesList Integration Tests', () => {
  test('complete note management workflow', () => {
    const { rerender } = render(<NotesList {...mockProps} />);
    
    // 1. Create a new note
    const createButton = screen.getByTitle('Create new note');
    fireEvent.click(createButton);
    expect(mockProps.onSave).toHaveBeenCalled();
    
    // 2. Search for a note
    const searchInput = screen.getByPlaceholderText('Search notes...');
    fireEvent.change(searchInput, { target: { value: 'Test' } });
    expect(screen.getByText('Test Note 1')).toBeInTheDocument();
    
    // 3. Pin a note
    const pinButtons = document.querySelectorAll('.pin-btn');
    fireEvent.click(pinButtons[0]);
    expect(mockProps.onPin).toHaveBeenCalled();
    
    // 4. Select a note
    const firstNote = screen.getByText('Test Note 1');
    fireEvent.click(firstNote.closest('.note-item'));
    expect(mockProps.onSelect).toHaveBeenCalledWith('1');
    
    // 5. Update props to show selection
    const updatedProps = { ...mockProps, selectedId: '1' };
    rerender(<NotesList {...updatedProps} />);
    
    const selectedNote = screen.getByText('Test Note 1').closest('.note-item');
    expect(selectedNote).toHaveClass('selected');
  });
});
