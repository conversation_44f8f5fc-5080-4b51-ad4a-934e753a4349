/* src/components/NotesList/NotesList.css */
.notes-list {
  width: 300px;
  height: 100vh;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.notes-list-header {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
}

.notes-list-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.search-section {
  padding: 1rem;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.search-input-container {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 0.875rem;
}

.search-input {
  width: 100%;
  padding: 0.5rem 0.75rem 0.5rem 2rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.875rem;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-section {
  padding: 0.5rem 1rem;
  background: white;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  gap: 0.5rem;
}

.sort-select,
.filter-select {
  flex: 1;
  padding: 0.375rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.75rem;
  outline: none;
}

.notes-count {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
  color: #6c757d;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.notes-list-content {
  flex: 1;
  overflow-y: auto;
  background: #f8f9fa;
}

.empty-state {
  padding: 2rem 1rem;
  text-align: center;
  color: #6c757d;
}

.empty-state p {
  margin-bottom: 1rem;
}

.note-item {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  transition: background-color 0.2s ease;
  background: white;
  margin-bottom: 1px;
}

.note-item:hover {
  background: #f8f9fa;
}

.note-item.selected {
  background: #e3f2fd;
  border-left: 3px solid #007bff;
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.note-title {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.encrypted-icon {
  color: #ffc107;
  font-size: 0.75rem;
}

.note-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.note-item:hover .note-actions {
  opacity: 1;
}

.pin-btn,
.delete-btn {
  padding: 0.25rem;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 2px;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.pin-btn {
  color: #6c757d;
}

.pin-btn:hover {
  color: #ffc107;
  background: rgba(255, 193, 7, 0.1);
}

.pin-btn.pinned {
  color: #ffc107;
}

.delete-btn {
  color: #6c757d;
}

.delete-btn:hover {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
}

.note-preview {
  margin: 0 0 0.5rem 0;
  font-size: 0.75rem;
  color: #6c757d;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.note-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.625rem;
  color: #adb5bd;
}

.note-date,
.note-word-count {
  font-size: 0.625rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .notes-list {
    width: 100%;
    height: auto;
    max-height: 40vh;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }
  
  .notes-list-content {
    max-height: 200px;
  }
  
  .note-item {
    padding: 0.75rem;
  }
  
  .note-actions {
    opacity: 1;
  }
}

@media (max-width: 480px) {
  .notes-list-header {
    padding: 0.75rem;
  }
  
  .search-section,
  .filter-section {
    padding: 0.75rem;
  }
  
  .filter-section {
    flex-direction: column;
  }
  
  .sort-select,
  .filter-select {
    margin-bottom: 0.25rem;
  }
}
