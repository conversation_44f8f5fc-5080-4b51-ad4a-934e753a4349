// src/components/NotesList/NotesList.js
import React, { useState, useMemo } from 'react';
import { FaPlus, FaSearch, FaPin, FaTrash, FaLock, FaDownload, FaUpload } from 'react-icons/fa';
import { createNote, exportNotes, importNotes } from '../../utils/storage';
import './NotesList.css';

const NotesList = ({ notes, onSelect, onDelete, onPin, selectedId, onSave }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('updatedAt');
  const [filterBy, setFilterBy] = useState('all');

  // Sort and filter notes
  const processedNotes = useMemo(() => {
    let filtered = notes;

    // Filter by type
    if (filterBy === 'pinned') {
      filtered = filtered.filter(note => note.pinned);
    } else if (filterBy === 'encrypted') {
      filtered = filtered.filter(note => note.encrypted);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(note =>
        note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        note.content.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Sort notes
    const sorted = [...filtered].sort((a, b) => {
      // Pinned notes always come first
      if (a.pinned && !b.pinned) return -1;
      if (!a.pinned && b.pinned) return 1;

      switch (sortBy) {
        case 'title':
          return a.title.localeCompare(b.title);
        case 'createdAt':
          return new Date(b.createdAt) - new Date(a.createdAt);
        case 'updatedAt':
        default:
          return new Date(b.updatedAt) - new Date(a.updatedAt);
      }
    });

    return sorted;
  }, [notes, searchTerm, sortBy, filterBy]);

  const handleCreateNote = () => {
    const newNote = createNote();
    onSave(newNote);
    onSelect(newNote.id);
  };

  const handleExport = () => {
    exportNotes(notes);
  };

  const handleImport = (event) => {
    const file = event.target.files[0];
    if (file) {
      importNotes(file)
        .then(importedNotes => {
          importedNotes.forEach(note => onSave(note));
          alert(`Successfully imported ${importedNotes.length} notes`);
        })
        .catch(error => {
          alert('Error importing notes: ' + error.message);
        });
    }
    event.target.value = '';
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays - 1} days ago`;
    
    return date.toLocaleDateString();
  };

  const truncateContent = (content, maxLength = 100) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return (
    <div className="notes-list">
      <div className="notes-list-header">
        <h2>Notes</h2>
        <div className="header-actions">
          <button 
            className="btn btn-primary" 
            onClick={handleCreateNote}
            title="Create new note"
          >
            <FaPlus />
          </button>
          <button 
            className="btn btn-secondary" 
            onClick={handleExport}
            title="Export notes"
          >
            <FaDownload />
          </button>
          <label className="btn btn-secondary" title="Import notes">
            <FaUpload />
            <input
              type="file"
              accept=".json"
              onChange={handleImport}
              style={{ display: 'none' }}
            />
          </label>
        </div>
      </div>

      <div className="search-section">
        <div className="search-input-container">
          <FaSearch className="search-icon" />
          <input
            type="text"
            placeholder="Search notes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
      </div>

      <div className="filter-section">
        <select 
          value={sortBy} 
          onChange={(e) => setSortBy(e.target.value)}
          className="sort-select"
        >
          <option value="updatedAt">Last Modified</option>
          <option value="createdAt">Date Created</option>
          <option value="title">Title</option>
        </select>

        <select 
          value={filterBy} 
          onChange={(e) => setFilterBy(e.target.value)}
          className="filter-select"
        >
          <option value="all">All Notes</option>
          <option value="pinned">Pinned</option>
          <option value="encrypted">Encrypted</option>
        </select>
      </div>

      <div className="notes-count">
        {processedNotes.length} {processedNotes.length === 1 ? 'note' : 'notes'}
      </div>

      <div className="notes-list-content">
        {processedNotes.length === 0 ? (
          <div className="empty-state">
            {searchTerm || filterBy !== 'all' ? (
              <p>No notes match your search criteria.</p>
            ) : (
              <div>
                <p>No notes yet.</p>
                <button className="btn btn-primary" onClick={handleCreateNote}>
                  Create your first note
                </button>
              </div>
            )}
          </div>
        ) : (
          processedNotes.map(note => (
            <div
              key={note.id}
              className={`note-item ${selectedId === note.id ? 'selected' : ''}`}
              onClick={() => onSelect(note.id)}
            >
              <div className="note-header">
                <h3 className="note-title">
                  {note.title || 'Untitled Note'}
                  {note.encrypted && <FaLock className="encrypted-icon" />}
                </h3>
                <div className="note-actions">
                  <button
                    className={`pin-btn ${note.pinned ? 'pinned' : ''}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      onPin(note.id);
                    }}
                    title={note.pinned ? 'Unpin note' : 'Pin note'}
                  >
                    <FaPin />
                  </button>
                  <button
                    className="delete-btn"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (window.confirm('Are you sure you want to delete this note?')) {
                        onDelete(note.id);
                      }
                    }}
                    title="Delete note"
                  >
                    <FaTrash />
                  </button>
                </div>
              </div>
              
              <p className="note-preview">
                {truncateContent(note.content)}
              </p>
              
              <div className="note-meta">
                <span className="note-date">
                  {formatDate(note.updatedAt)}
                </span>
                <span className="note-word-count">
                  {note.wordCount || 0} words
                </span>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default NotesList;
