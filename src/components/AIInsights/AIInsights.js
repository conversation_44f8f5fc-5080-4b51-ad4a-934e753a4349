// src/components/AIInsights/AIInsights.js
import React, { useState, useEffect } from 'react';
import { 
  FaTimes, 
  FaLightbulb, 
  FaChartBar, 
  FaKeyboard, 
  FaRobot,
  FaSpinner,
  FaRefresh
} from 'react-icons/fa';
import { generateAIInsights, generateAISummary, suggestRelatedNotes } from '../../utils/aiServices';
import './AIInsights.css';

const AIInsights = ({ content, onClose, allNotes = [] }) => {
  const [insights, setInsights] = useState([]);
  const [summary, setSummary] = useState('');
  const [relatedNotes, setRelatedNotes] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('insights');
  const [error, setError] = useState(null);

  const loadInsights = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Load insights in parallel
      const [insightsData, summaryData, relatedData] = await Promise.all([
        generateAIInsights(content),
        generateAISummary(content),
        Promise.resolve(suggestRelatedNotes({ content }, allNotes))
      ]);

      setInsights(insightsData || []);
      setSummary(summaryData || 'No summary available.');
      setRelatedNotes(relatedData || []);
    } catch (error) {
      console.error('Error loading AI insights:', error);
      setError('Failed to load AI insights. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (content) {
      loadInsights();
    }
  }, [content, loadInsights]);

  const handleRefresh = () => {
    loadInsights();
  };

  const getInsightIcon = (type) => {
    switch (type) {
      case 'length':
      case 'readability':
        return <FaChartBar />;
      case 'keywords':
        return <FaKeyboard />;
      case 'ai-analysis':
        return <FaRobot />;
      default:
        return <FaLightbulb />;
    }
  };

  const getInsightColor = (priority) => {
    switch (priority) {
      case 'high':
        return '#dc3545';
      case 'medium':
        return '#ffc107';
      case 'low':
        return '#28a745';
      default:
        return '#17a2b8';
    }
  };

  const renderInsights = () => {
    if (isLoading) {
      return (
        <div className="insights-loading">
          <FaSpinner className="spinner" />
          <p>Analyzing your content...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className="insights-error">
          <p>{error}</p>
          <button className="btn btn-primary" onClick={handleRefresh}>
            <FaRefresh /> Try Again
          </button>
        </div>
      );
    }

    if (insights.length === 0) {
      return (
        <div className="insights-empty">
          <FaLightbulb className="empty-icon" />
          <p>No insights available for this content.</p>
        </div>
      );
    }

    return (
      <div className="insights-list">
        {insights.map((insight, index) => (
          <div 
            key={index} 
            className="insight-item"
            style={{ borderLeftColor: getInsightColor(insight.priority) }}
          >
            <div className="insight-header">
              <div className="insight-icon">
                {getInsightIcon(insight.type)}
              </div>
              <h4 className="insight-title">{insight.title}</h4>
            </div>
            <p className="insight-message">{insight.message}</p>
          </div>
        ))}
      </div>
    );
  };

  const renderSummary = () => {
    if (isLoading) {
      return (
        <div className="insights-loading">
          <FaSpinner className="spinner" />
          <p>Generating summary...</p>
        </div>
      );
    }

    return (
      <div className="summary-content">
        <div className="summary-text">
          {summary}
        </div>
        <div className="summary-stats">
          <div className="stat-item">
            <span className="stat-label">Word Count:</span>
            <span className="stat-value">
              {content.split(/\s+/).filter(word => word.length > 0).length}
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Characters:</span>
            <span className="stat-value">{content.length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Paragraphs:</span>
            <span className="stat-value">
              {content.split(/\n\s*\n/).filter(p => p.trim().length > 0).length}
            </span>
          </div>
        </div>
      </div>
    );
  };

  const renderRelatedNotes = () => {
    if (relatedNotes.length === 0) {
      return (
        <div className="insights-empty">
          <p>No related notes found.</p>
        </div>
      );
    }

    return (
      <div className="related-notes-list">
        {relatedNotes.map(note => (
          <div key={note.id} className="related-note-item">
            <h4 className="related-note-title">{note.title || 'Untitled Note'}</h4>
            <p className="related-note-preview">
              {note.content.substring(0, 100)}...
            </p>
            <div className="related-note-meta">
              <span className="note-date">
                {new Date(note.updatedAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="ai-insights-panel">
      <div className="insights-header">
        <h3>AI Insights</h3>
        <div className="insights-actions">
          <button 
            className="btn btn-sm btn-secondary"
            onClick={handleRefresh}
            disabled={isLoading}
            title="Refresh insights"
          >
            <FaRefresh className={isLoading ? 'spinner' : ''} />
          </button>
          <button 
            className="btn btn-sm btn-secondary"
            onClick={onClose}
            title="Close insights"
          >
            <FaTimes />
          </button>
        </div>
      </div>

      <div className="insights-tabs">
        <button
          className={`tab-btn ${activeTab === 'insights' ? 'active' : ''}`}
          onClick={() => setActiveTab('insights')}
        >
          <FaLightbulb /> Insights
        </button>
        <button
          className={`tab-btn ${activeTab === 'summary' ? 'active' : ''}`}
          onClick={() => setActiveTab('summary')}
        >
          <FaChartBar /> Summary
        </button>
        <button
          className={`tab-btn ${activeTab === 'related' ? 'active' : ''}`}
          onClick={() => setActiveTab('related')}
        >
          <FaKeyboard /> Related
        </button>
      </div>

      <div className="insights-content">
        {activeTab === 'insights' && renderInsights()}
        {activeTab === 'summary' && renderSummary()}
        {activeTab === 'related' && renderRelatedNotes()}
      </div>
    </div>
  );
};

export default AIInsights;
