/* src/components/AIInsights/AIInsights.css */
.ai-insights-panel {
  width: 350px;
  height: 100%;
  background: white;
  border-left: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.insights-header {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.insights-header h3 {
  margin: 0;
  font-size: 1.125rem;
  color: #333;
}

.insights-actions {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn:hover:not(:disabled) {
  background: #f8f9fa;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-sm {
  padding: 0.375rem;
  font-size: 0.875rem;
}

.btn-secondary {
  color: #6c757d;
}

.btn-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
  border-color: #0056b3;
}

.insights-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.tab-btn {
  flex: 1;
  padding: 0.75rem 0.5rem;
  border: none;
  background: transparent;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.8125rem;
  border-bottom: 2px solid transparent;
}

.tab-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #495057;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
  background: white;
}

.insights-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.insights-loading,
.insights-error,
.insights-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
  color: #6c757d;
}

.spinner {
  animation: spin 1s linear infinite;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #007bff;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.empty-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #adb5bd;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.insight-item {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #17a2b8;
  transition: transform 0.2s ease;
}

.insight-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.insight-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.insight-icon {
  color: #17a2b8;
  font-size: 1rem;
}

.insight-title {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
}

.insight-message {
  margin: 0;
  font-size: 0.8125rem;
  line-height: 1.4;
  color: #495057;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.summary-text {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #495057;
  border-left: 4px solid #28a745;
}

.summary-stats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f1f3f4;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 0.8125rem;
  color: #6c757d;
}

.stat-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
}

.related-notes-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.related-note-item {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #ffc107;
  cursor: pointer;
  transition: all 0.2s ease;
}

.related-note-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.related-note-title {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
}

.related-note-preview {
  margin: 0 0 0.5rem 0;
  font-size: 0.8125rem;
  line-height: 1.4;
  color: #6c757d;
}

.related-note-meta {
  display: flex;
  justify-content: flex-end;
}

.note-date {
  font-size: 0.75rem;
  color: #adb5bd;
}

/* Responsive design */
@media (max-width: 768px) {
  .ai-insights-panel {
    width: 100%;
    height: auto;
    max-height: 50vh;
    border-left: none;
    border-top: 1px solid #e9ecef;
  }
  
  .insights-header {
    padding: 0.75rem;
  }
  
  .insights-header h3 {
    font-size: 1rem;
  }
  
  .insights-content {
    padding: 0.75rem;
  }
  
  .tab-btn {
    padding: 0.5rem 0.25rem;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .insights-tabs {
    flex-direction: column;
  }
  
  .tab-btn {
    padding: 0.75rem;
    border-bottom: 1px solid #e9ecef;
    border-right: none;
  }
  
  .tab-btn.active {
    border-bottom-color: #e9ecef;
    border-left: 3px solid #007bff;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .ai-insights-panel {
    background: #1a1a1a;
    border-color: #404040;
    color: #e9ecef;
  }
  
  .insights-header {
    background: #2d2d2d;
    border-color: #404040;
  }
  
  .insights-header h3 {
    color: #e9ecef;
  }
  
  .btn {
    background: #404040;
    border-color: #6c757d;
    color: #e9ecef;
  }
  
  .btn:hover:not(:disabled) {
    background: #495057;
  }
  
  .insights-tabs {
    background: #2d2d2d;
    border-color: #404040;
  }
  
  .tab-btn {
    color: #adb5bd;
  }
  
  .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #e9ecef;
  }
  
  .tab-btn.active {
    background: #1a1a1a;
    color: #007bff;
  }
  
  .insight-item,
  .summary-text,
  .related-note-item {
    background: #2d2d2d;
  }
  
  .related-note-item:hover {
    background: #404040;
  }
  
  .insight-title,
  .related-note-title,
  .stat-value {
    color: #e9ecef;
  }
  
  .insight-message,
  .summary-text,
  .related-note-preview {
    color: #ced4da;
  }
  
  .stat-label {
    color: #adb5bd;
  }
  
  .stat-item {
    border-color: #404040;
  }
}
