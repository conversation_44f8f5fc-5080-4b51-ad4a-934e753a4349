// src/components/GrammarChecker/GrammarChecker.js
import React, { useState, useEffect } from 'react';
import { FaExclamationTriangle, FaCheck, FaTimes } from 'react-icons/fa';
import './GrammarChecker.css';

const GrammarChecker = ({ issues = [] }) => {
  const [visibleIssues, setVisibleIssues] = useState([]);
  const [selectedIssue, setSelectedIssue] = useState(null);

  useEffect(() => {
    // Filter and process issues for display
    const processedIssues = issues.map((issue, index) => ({
      ...issue,
      id: `issue-${index}`,
      isVisible: true
    }));
    
    setVisibleIssues(processedIssues);
  }, [issues]);

  const handleIssueClick = (issue) => {
    setSelectedIssue(issue);
  };

  const handleDismissIssue = (issueId) => {
    setVisibleIssues(prev => 
      prev.map(issue => 
        issue.id === issueId 
          ? { ...issue, isVisible: false }
          : issue
      )
    );
    
    if (selectedIssue && selectedIssue.id === issueId) {
      setSelectedIssue(null);
    }
  };

  const handleApplySuggestion = (issue) => {
    if (issue.suggestion) {
      // In a real implementation, this would apply the suggestion to the editor
      console.log('Applying suggestion:', issue.suggestion);
      handleDismissIssue(issue.id);
    }
  };

  const getIssueIcon = (type) => {
    switch (type) {
      case 'grammar':
        return <FaExclamationTriangle className="issue-icon grammar-icon" />;
      case 'spelling':
        return <FaExclamationTriangle className="issue-icon spelling-icon" />;
      case 'style':
        return <FaCheck className="issue-icon style-icon" />;
      default:
        return <FaExclamationTriangle className="issue-icon" />;
    }
  };

  const getIssueClass = (type) => {
    switch (type) {
      case 'grammar':
        return 'grammar-issue';
      case 'spelling':
        return 'spelling-issue';
      case 'style':
        return 'style-issue';
      default:
        return 'general-issue';
    }
  };

  const visibleIssuesList = visibleIssues.filter(issue => issue.isVisible);

  if (visibleIssuesList.length === 0) {
    return null;
  }

  return (
    <div className="grammar-checker">
      <div className="grammar-checker-summary">
        <span className="issues-count">
          {visibleIssuesList.length} {visibleIssuesList.length === 1 ? 'issue' : 'issues'} found
        </span>
      </div>

      <div className="grammar-issues-list">
        {visibleIssuesList.map(issue => (
          <div
            key={issue.id}
            className={`grammar-issue-item ${getIssueClass(issue.type)} ${
              selectedIssue && selectedIssue.id === issue.id ? 'selected' : ''
            }`}
            onClick={() => handleIssueClick(issue)}
          >
            <div className="issue-header">
              {getIssueIcon(issue.type)}
              <span className="issue-text">"{issue.text}"</span>
              <button
                className="dismiss-btn"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDismissIssue(issue.id);
                }}
                title="Dismiss issue"
              >
                <FaTimes />
              </button>
            </div>
            
            <div className="issue-message">
              {issue.message}
            </div>

            {issue.suggestion && (
              <div className="issue-suggestion">
                <span className="suggestion-label">Suggestion:</span>
                <span className="suggestion-text">"{issue.suggestion}"</span>
                <button
                  className="apply-suggestion-btn"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleApplySuggestion(issue);
                  }}
                  title="Apply suggestion"
                >
                  Apply
                </button>
              </div>
            )}
          </div>
        ))}
      </div>

      {selectedIssue && (
        <div className="issue-details-popup">
          <div className="popup-header">
            <h4>Issue Details</h4>
            <button
              className="popup-close-btn"
              onClick={() => setSelectedIssue(null)}
            >
              <FaTimes />
            </button>
          </div>
          
          <div className="popup-content">
            <div className="detail-item">
              <strong>Text:</strong> "{selectedIssue.text}"
            </div>
            
            <div className="detail-item">
              <strong>Issue:</strong> {selectedIssue.message}
            </div>
            
            <div className="detail-item">
              <strong>Type:</strong> {selectedIssue.type}
            </div>
            
            <div className="detail-item">
              <strong>Position:</strong> Characters {selectedIssue.start} - {selectedIssue.end}
            </div>

            {selectedIssue.suggestion && (
              <div className="detail-item">
                <strong>Suggestion:</strong> "{selectedIssue.suggestion}"
              </div>
            )}
          </div>

          <div className="popup-actions">
            {selectedIssue.suggestion && (
              <button
                className="btn btn-primary"
                onClick={() => handleApplySuggestion(selectedIssue)}
              >
                Apply Suggestion
              </button>
            )}
            <button
              className="btn btn-secondary"
              onClick={() => handleDismissIssue(selectedIssue.id)}
            >
              Dismiss
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default GrammarChecker;
