/* src/components/GrammarChecker/GrammarChecker.css */
.grammar-checker {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e9ecef;
  max-height: 200px;
  overflow-y: auto;
  z-index: 100;
}

.grammar-checker-summary {
  padding: 0.5rem 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 0.8125rem;
  color: #6c757d;
}

.issues-count {
  font-weight: 500;
}

.grammar-issues-list {
  max-height: 150px;
  overflow-y: auto;
}

.grammar-issue-item {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.grammar-issue-item:hover {
  background: #f8f9fa;
}

.grammar-issue-item.selected {
  background: #e3f2fd;
  border-left: 3px solid #007bff;
}

.grammar-issue-item:last-child {
  border-bottom: none;
}

.issue-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.issue-icon {
  font-size: 0.75rem;
}

.grammar-icon {
  color: #dc3545;
}

.spelling-icon {
  color: #ffc107;
}

.style-icon {
  color: #17a2b8;
}

.issue-text {
  font-weight: 500;
  color: #333;
  font-size: 0.8125rem;
  background: rgba(255, 193, 7, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 2px;
}

.dismiss-btn {
  margin-left: auto;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 2px;
  transition: all 0.2s ease;
  font-size: 0.75rem;
}

.dismiss-btn:hover {
  color: #495057;
  background: rgba(0, 0, 0, 0.05);
}

.issue-message {
  font-size: 0.8125rem;
  color: #495057;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.issue-suggestion {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6c757d;
  background: #f8f9fa;
  padding: 0.375rem 0.5rem;
  border-radius: 4px;
  margin-top: 0.5rem;
}

.suggestion-label {
  font-weight: 500;
}

.suggestion-text {
  color: #28a745;
  font-weight: 500;
  background: rgba(40, 167, 69, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 2px;
}

.apply-suggestion-btn {
  margin-left: auto;
  background: #28a745;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.75rem;
  transition: background-color 0.2s ease;
}

.apply-suggestion-btn:hover {
  background: #1e7e34;
}

/* Issue type specific styles */
.grammar-issue {
  border-left: 3px solid #dc3545;
}

.spelling-issue {
  border-left: 3px solid #ffc107;
}

.style-issue {
  border-left: 3px solid #17a2b8;
}

.general-issue {
  border-left: 3px solid #6c757d;
}

/* Issue details popup */
.issue-details-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  width: 90%;
  z-index: 1000;
  animation: fadeInScale 0.2s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.popup-header h4 {
  margin: 0;
  font-size: 1rem;
  color: #333;
}

.popup-close-btn {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.popup-close-btn:hover {
  color: #495057;
  background: rgba(0, 0, 0, 0.05);
}

.popup-content {
  padding: 1rem;
}

.detail-item {
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.4;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item strong {
  color: #333;
  display: inline-block;
  min-width: 80px;
}

.popup-actions {
  padding: 1rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.btn {
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.btn-primary:hover {
  background: #0056b3;
  border-color: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
  border-color: #6c757d;
}

.btn-secondary:hover {
  background: #545b62;
  border-color: #545b62;
}

/* Responsive design */
@media (max-width: 768px) {
  .grammar-checker {
    max-height: 150px;
  }
  
  .grammar-issues-list {
    max-height: 100px;
  }
  
  .grammar-issue-item {
    padding: 0.5rem 0.75rem;
  }
  
  .issue-suggestion {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .apply-suggestion-btn {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .issue-details-popup {
    width: 95%;
    max-width: none;
  }
  
  .popup-actions {
    flex-direction: column-reverse;
  }
  
  .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .grammar-checker-summary {
    padding: 0.375rem 0.5rem;
  }
  
  .grammar-issue-item {
    padding: 0.5rem;
  }
  
  .issue-header {
    flex-wrap: wrap;
  }
  
  .dismiss-btn {
    order: -1;
    margin-left: 0;
    margin-right: auto;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .grammar-checker {
    background: #1a1a1a;
    border-color: #404040;
    color: #e9ecef;
  }
  
  .grammar-checker-summary {
    background: #2d2d2d;
    border-color: #404040;
    color: #adb5bd;
  }
  
  .grammar-issue-item:hover {
    background: #2d2d2d;
  }
  
  .grammar-issue-item.selected {
    background: #1a3a52;
  }
  
  .issue-text {
    color: #e9ecef;
    background: rgba(255, 193, 7, 0.2);
  }
  
  .issue-message {
    color: #ced4da;
  }
  
  .issue-suggestion {
    background: #2d2d2d;
    color: #adb5bd;
  }
  
  .suggestion-text {
    color: #28a745;
    background: rgba(40, 167, 69, 0.2);
  }
  
  .dismiss-btn,
  .popup-close-btn {
    color: #adb5bd;
  }
  
  .dismiss-btn:hover,
  .popup-close-btn:hover {
    color: #e9ecef;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .issue-details-popup {
    background: #1a1a1a;
    border-color: #404040;
  }
  
  .popup-header {
    background: #2d2d2d;
    border-color: #404040;
  }
  
  .popup-header h4 {
    color: #e9ecef;
  }
  
  .detail-item strong {
    color: #e9ecef;
  }
  
  .popup-actions {
    border-color: #404040;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .grammar-issue-item,
  .dismiss-btn,
  .apply-suggestion-btn,
  .popup-close-btn,
  .btn {
    transition: none;
  }
  
  .issue-details-popup {
    animation: none;
  }
}
