/* src/components/GlossaryPopup/GlossaryPopup.css */
.glossary-popup {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 300px;
  min-width: 250px;
  animation: fadeInUp 0.2s ease-out;
  overflow: hidden;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.glossary-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.glossary-term-title {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
  text-transform: capitalize;
}

.glossary-close-btn {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 2px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.glossary-close-btn:hover {
  color: #495057;
  background: rgba(0, 0, 0, 0.05);
}

.glossary-popup-content {
  padding: 1rem;
}

.glossary-definition {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #495057;
}

.glossary-popup-footer {
  border-top: 1px solid #f1f3f4;
  padding-top: 0.5rem;
}

.glossary-source {
  color: #6c757d;
  font-size: 0.75rem;
  font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
  .glossary-popup {
    max-width: 280px;
    min-width: 200px;
  }
  
  .glossary-popup-header {
    padding: 0.5rem 0.75rem;
  }
  
  .glossary-term-title {
    font-size: 0.8125rem;
  }
  
  .glossary-popup-content {
    padding: 0.75rem;
  }
  
  .glossary-definition {
    font-size: 0.8125rem;
  }
}

@media (max-width: 480px) {
  .glossary-popup {
    max-width: 250px;
    min-width: 180px;
    left: 10px !important;
    right: 10px !important;
    width: calc(100vw - 20px) !important;
    max-width: none !important;
  }
  
  .glossary-popup-header {
    padding: 0.5rem;
  }
  
  .glossary-popup-content {
    padding: 0.5rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .glossary-popup {
    background: #2d2d2d;
    border-color: #404040;
    color: #e9ecef;
  }
  
  .glossary-popup-header {
    background: #404040;
    border-color: #6c757d;
  }
  
  .glossary-term-title {
    color: #e9ecef;
  }
  
  .glossary-close-btn {
    color: #adb5bd;
  }
  
  .glossary-close-btn:hover {
    color: #e9ecef;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .glossary-definition {
    color: #ced4da;
  }
  
  .glossary-popup-footer {
    border-color: #6c757d;
  }
  
  .glossary-source {
    color: #adb5bd;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .glossary-popup {
    border-width: 2px;
    border-color: currentColor;
  }
  
  .glossary-popup-header {
    border-bottom-width: 2px;
  }
  
  .glossary-close-btn {
    border: 1px solid currentColor;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .glossary-popup {
    animation: none;
  }
  
  .glossary-close-btn {
    transition: none;
  }
}
