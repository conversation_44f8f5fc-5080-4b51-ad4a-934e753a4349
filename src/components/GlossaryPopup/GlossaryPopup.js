// src/components/GlossaryPopup/GlossaryPopup.js
import React, { useEffect, useRef } from 'react';
import { FaTimes } from 'react-icons/fa';
import './GlossaryPopup.css';

const GlossaryPopup = ({ term, onClose }) => {
  const popupRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        onClose();
      }
    };

    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscape);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);

  if (!term) return null;

  const popupStyle = {
    position: 'fixed',
    left: Math.min(term.x, window.innerWidth - 320),
    top: Math.min(term.y + 10, window.innerHeight - 200),
    zIndex: 1000
  };

  return (
    <div
      ref={popupRef}
      className="glossary-popup"
      style={popupStyle}
    >
      <div className="glossary-popup-header">
        <h4 className="glossary-term-title">{term.term}</h4>
        <button
          className="glossary-close-btn"
          onClick={onClose}
          aria-label="Close definition"
        >
          <FaTimes />
        </button>
      </div>
      
      <div className="glossary-popup-content">
        <p className="glossary-definition">
          {term.definition}
        </p>
        
        <div className="glossary-popup-footer">
          <small className="glossary-source">
            Auto-detected term
          </small>
        </div>
      </div>
    </div>
  );
};

export default GlossaryPopup;
