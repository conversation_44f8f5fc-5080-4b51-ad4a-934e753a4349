/* src/components/Toolbar/Toolbar.css */
.toolbar {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  gap: 0.5rem;
  flex-wrap: wrap;
  min-height: 60px;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background: #ced4da;
  margin: 0 0.5rem;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid transparent;
  border-radius: 4px;
  background: transparent;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.toolbar-btn:hover {
  background: #e9ecef;
  border-color: #ced4da;
}

.toolbar-btn:active {
  background: #dee2e6;
}

.toolbar-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.toolbar-btn.active:hover {
  background: #0056b3;
  border-color: #0056b3;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-btn:disabled:hover {
  background: transparent;
  border-color: transparent;
}

/* Font size control */
.font-size-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
}

.control-icon {
  color: #6c757d;
  font-size: 0.75rem;
}

.font-size-select {
  border: none;
  background: transparent;
  font-size: 0.75rem;
  color: #495057;
  outline: none;
  cursor: pointer;
  min-width: 50px;
}

/* Color control */
.color-control {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
}

.color-picker {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  outline: none;
}

.color-picker::-webkit-color-swatch-wrapper {
  padding: 0;
}

.color-picker::-webkit-color-swatch {
  border: 1px solid #ced4da;
  border-radius: 2px;
}

.color-presets {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 0.5rem;
  display: none;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.25rem;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.color-control:hover .color-presets {
  display: grid;
}

.color-preset {
  width: 20px;
  height: 20px;
  border: 1px solid #ced4da;
  border-radius: 2px;
  cursor: pointer;
  transition: transform 0.1s ease;
}

.color-preset:hover {
  transform: scale(1.1);
  border-color: #007bff;
}

/* Responsive design */
@media (max-width: 768px) {
  .toolbar {
    padding: 0.5rem;
    gap: 0.25rem;
    min-height: auto;
  }
  
  .toolbar-section {
    gap: 0.125rem;
  }
  
  .toolbar-btn {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
  
  .toolbar-divider {
    height: 20px;
    margin: 0 0.25rem;
  }
  
  .font-size-control,
  .color-control {
    padding: 0.125rem 0.25rem;
  }
  
  .font-size-select {
    font-size: 0.625rem;
    min-width: 40px;
  }
  
  .color-picker {
    width: 20px;
    height: 20px;
  }
  
  .control-icon {
    font-size: 0.625rem;
  }
}

@media (max-width: 480px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
    padding: 0.5rem;
  }
  
  .toolbar-section {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .toolbar-divider {
    display: none;
  }
  
  .font-size-control,
  .color-control {
    justify-content: center;
    min-width: 120px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .toolbar {
    background: #2d2d2d;
    border-color: #404040;
  }
  
  .toolbar-btn {
    color: #e9ecef;
  }
  
  .toolbar-btn:hover {
    background: #404040;
    border-color: #6c757d;
  }
  
  .toolbar-btn:active {
    background: #495057;
  }
  
  .toolbar-divider {
    background: #6c757d;
  }
  
  .font-size-control,
  .color-control {
    background: #404040;
    border-color: #6c757d;
  }
  
  .control-icon {
    color: #adb5bd;
  }
  
  .font-size-select {
    color: #e9ecef;
    background: transparent;
  }
  
  .color-presets {
    background: #404040;
    border-color: #6c757d;
  }
  
  .color-preset {
    border-color: #6c757d;
  }
  
  .color-preset:hover {
    border-color: #007bff;
  }
}

/* Print styles */
@media print {
  .toolbar {
    display: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .toolbar-btn {
    border-color: currentColor;
  }
  
  .toolbar-btn:hover {
    background: currentColor;
    color: white;
  }
  
  .toolbar-divider {
    background: currentColor;
  }
}
