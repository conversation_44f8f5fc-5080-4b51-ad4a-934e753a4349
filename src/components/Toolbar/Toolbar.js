// src/components/Toolbar/Toolbar.js
import React, { useState, useEffect } from 'react';
import { 
  FaBold, 
  FaItalic, 
  FaUnderline, 
  FaAlignLeft, 
  FaAlignCenter, 
  FaAlignRight,
  FaUndo,
  FaRedo,
  FaFont,
  FaPalette,
  FaListUl,
  FaListOl,
  FaQuoteLeft
} from 'react-icons/fa';
import './Toolbar.css';

const Toolbar = ({ onFormat }) => {
  const [activeFormats, setActiveFormats] = useState(new Set());
  const [fontSize, setFontSize] = useState(16);
  const [textColor, setTextColor] = useState('#000000');

  // Check which formats are currently active
  useEffect(() => {
    const checkFormats = () => {
      const formats = new Set();
      
      try {
        if (document.queryCommandState('bold')) formats.add('bold');
        if (document.queryCommandState('italic')) formats.add('italic');
        if (document.queryCommandState('underline')) formats.add('underline');
        if (document.queryCommandState('justifyLeft')) formats.add('justifyLeft');
        if (document.queryCommandState('justifyCenter')) formats.add('justifyCenter');
        if (document.queryCommandState('justifyRight')) formats.add('justifyRight');
        if (document.queryCommandState('insertUnorderedList')) formats.add('insertUnorderedList');
        if (document.queryCommandState('insertOrderedList')) formats.add('insertOrderedList');
      } catch (error) {
        // Some browsers may not support all commands
        console.warn('Format checking not supported:', error);
      }
      
      setActiveFormats(formats);
    };

    // Check formats when selection changes
    document.addEventListener('selectionchange', checkFormats);
    
    return () => {
      document.removeEventListener('selectionchange', checkFormats);
    };
  }, []);

  const handleFormatClick = (command, value = null) => {
    onFormat(command, value);
    
    // Update active formats after a short delay
    setTimeout(() => {
      const formats = new Set(activeFormats);
      if (formats.has(command)) {
        formats.delete(command);
      } else {
        formats.add(command);
      }
      setActiveFormats(formats);
    }, 10);
  };

  const handleFontSizeChange = (newSize) => {
    setFontSize(newSize);
    onFormat('fontSize', newSize + 'px');
  };

  const handleColorChange = (color) => {
    setTextColor(color);
    onFormat('foreColor', color);
  };

  const fontSizes = [10, 12, 14, 16, 18, 20, 24, 28, 32, 36, 48];
  const commonColors = [
    '#000000', '#333333', '#666666', '#999999',
    '#FF0000', '#00FF00', '#0000FF', '#FFFF00',
    '#FF00FF', '#00FFFF', '#FFA500', '#800080'
  ];

  return (
    <div className="toolbar">
      <div className="toolbar-section">
        <button
          className={`toolbar-btn ${activeFormats.has('bold') ? 'active' : ''}`}
          onClick={() => handleFormatClick('bold')}
          title="Bold (Ctrl+B)"
        >
          <FaBold />
        </button>
        
        <button
          className={`toolbar-btn ${activeFormats.has('italic') ? 'active' : ''}`}
          onClick={() => handleFormatClick('italic')}
          title="Italic (Ctrl+I)"
        >
          <FaItalic />
        </button>
        
        <button
          className={`toolbar-btn ${activeFormats.has('underline') ? 'active' : ''}`}
          onClick={() => handleFormatClick('underline')}
          title="Underline (Ctrl+U)"
        >
          <FaUnderline />
        </button>
      </div>

      <div className="toolbar-divider"></div>

      <div className="toolbar-section">
        <button
          className={`toolbar-btn ${activeFormats.has('justifyLeft') ? 'active' : ''}`}
          onClick={() => handleFormatClick('justifyLeft')}
          title="Align Left"
        >
          <FaAlignLeft />
        </button>
        
        <button
          className={`toolbar-btn ${activeFormats.has('justifyCenter') ? 'active' : ''}`}
          onClick={() => handleFormatClick('justifyCenter')}
          title="Align Center"
        >
          <FaAlignCenter />
        </button>
        
        <button
          className={`toolbar-btn ${activeFormats.has('justifyRight') ? 'active' : ''}`}
          onClick={() => handleFormatClick('justifyRight')}
          title="Align Right"
        >
          <FaAlignRight />
        </button>
      </div>

      <div className="toolbar-divider"></div>

      <div className="toolbar-section">
        <button
          className={`toolbar-btn ${activeFormats.has('insertUnorderedList') ? 'active' : ''}`}
          onClick={() => handleFormatClick('insertUnorderedList')}
          title="Bullet List"
        >
          <FaListUl />
        </button>
        
        <button
          className={`toolbar-btn ${activeFormats.has('insertOrderedList') ? 'active' : ''}`}
          onClick={() => handleFormatClick('insertOrderedList')}
          title="Numbered List"
        >
          <FaListOl />
        </button>
        
        <button
          className="toolbar-btn"
          onClick={() => handleFormatClick('formatBlock', 'blockquote')}
          title="Quote"
        >
          <FaQuoteLeft />
        </button>
      </div>

      <div className="toolbar-divider"></div>

      <div className="toolbar-section">
        <div className="font-size-control">
          <FaFont className="control-icon" />
          <select
            value={fontSize}
            onChange={(e) => handleFontSizeChange(parseInt(e.target.value, 10))}
            className="font-size-select"
            title="Font Size"
          >
            {fontSizes.map(size => (
              <option key={size} value={size}>{size}px</option>
            ))}
          </select>
        </div>
        
        <div className="color-control">
          <FaPalette className="control-icon" />
          <input
            type="color"
            value={textColor}
            onChange={(e) => handleColorChange(e.target.value)}
            className="color-picker"
            title="Text Color"
          />
          <div className="color-presets">
            {commonColors.map(color => (
              <button
                key={color}
                className="color-preset"
                style={{ backgroundColor: color }}
                onClick={() => handleColorChange(color)}
                title={`Set color to ${color}`}
              />
            ))}
          </div>
        </div>
      </div>

      <div className="toolbar-divider"></div>

      <div className="toolbar-section">
        <button
          className="toolbar-btn"
          onClick={() => handleFormatClick('undo')}
          title="Undo (Ctrl+Z)"
        >
          <FaUndo />
        </button>
        
        <button
          className="toolbar-btn"
          onClick={() => handleFormatClick('redo')}
          title="Redo (Ctrl+Y)"
        >
          <FaRedo />
        </button>
      </div>
    </div>
  );
};

export default Toolbar;
