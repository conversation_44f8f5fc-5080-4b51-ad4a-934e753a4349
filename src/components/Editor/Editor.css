/* src/components/Editor/Editor.css */
.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: white;
  overflow: hidden;
}

.editor-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
  text-align: center;
}

.editor-empty-state h3 {
  margin-bottom: 0.5rem;
  color: #495057;
}

.editor-header {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 1rem;
  background: #f8f9fa;
}

.editor-title {
  flex: 1;
  border: none;
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  background: transparent;
  outline: none;
  padding: 0.5rem 0;
}

.editor-title::placeholder {
  color: #adb5bd;
}

.editor-actions {
  display: flex;
  gap: 0.5rem;
}

.editor-actions .btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.editor-actions .btn:hover {
  background: #f8f9fa;
}

.btn-warning {
  background: #ffc107 !important;
  border-color: #ffc107 !important;
  color: #212529 !important;
}

.btn-info {
  background: #17a2b8 !important;
  border-color: #17a2b8 !important;
  color: white !important;
}

.editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.editor-textarea {
  flex: 1;
  padding: 2rem;
  font-size: 1rem;
  line-height: 1.6;
  color: #333;
  outline: none;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.editor-textarea:empty::before {
  content: 'Start writing your note...';
  color: #adb5bd;
  font-style: italic;
}

/* Rich text formatting styles */
.editor-textarea b,
.editor-textarea strong {
  font-weight: bold;
}

.editor-textarea i,
.editor-textarea em {
  font-style: italic;
}

.editor-textarea u {
  text-decoration: underline;
}

.editor-textarea[style*="text-align: center"] {
  text-align: center;
}

.editor-textarea[style*="text-align: right"] {
  text-align: right;
}

.editor-textarea[style*="text-align: left"] {
  text-align: left;
}

/* Glossary term highlighting */
.glossary-term {
  background: rgba(255, 193, 7, 0.2);
  border-bottom: 1px dotted #ffc107;
  cursor: help;
  transition: background-color 0.2s ease;
}

.glossary-term:hover {
  background: rgba(255, 193, 7, 0.3);
}

/* Grammar error highlighting */
.grammar-error {
  border-bottom: 2px wavy #dc3545;
  cursor: help;
}

.grammar-suggestion {
  border-bottom: 2px wavy #28a745;
  cursor: help;
}

/* Selection styles */
.editor-textarea::selection {
  background: rgba(0, 123, 255, 0.2);
}

.editor-textarea::-moz-selection {
  background: rgba(0, 123, 255, 0.2);
}

/* Focus styles */
.editor-textarea:focus {
  box-shadow: inset 0 0 0 2px rgba(0, 123, 255, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .editor-container {
    height: auto;
    min-height: 60vh;
  }
  
  .editor-header {
    padding: 0.75rem;
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .editor-title {
    font-size: 1.25rem;
  }
  
  .editor-actions {
    justify-content: center;
  }
  
  .editor-textarea {
    padding: 1rem;
    font-size: 0.9rem;
  }
  
  .editor-content {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .editor-header {
    padding: 0.5rem;
  }
  
  .editor-title {
    font-size: 1.125rem;
  }
  
  .editor-textarea {
    padding: 0.75rem;
    font-size: 0.875rem;
  }
  
  .editor-actions .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
  }
}

/* Print styles */
@media print {
  .editor-header,
  .toolbar,
  .editor-actions {
    display: none;
  }
  
  .editor-container {
    height: auto;
  }
  
  .editor-textarea {
    padding: 0;
    font-size: 12pt;
    line-height: 1.5;
    color: black;
  }
  
  .glossary-term,
  .grammar-error,
  .grammar-suggestion {
    background: none;
    border: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .editor-container {
    background: #1a1a1a;
    color: #e9ecef;
  }
  
  .editor-header {
    background: #2d2d2d;
    border-color: #404040;
  }
  
  .editor-title {
    color: #e9ecef;
    background: transparent;
  }
  
  .editor-title::placeholder {
    color: #6c757d;
  }
  
  .editor-textarea {
    color: #e9ecef;
  }
  
  .editor-textarea:empty::before {
    color: #6c757d;
  }
  
  .editor-actions .btn {
    background: #404040;
    border-color: #6c757d;
    color: #e9ecef;
  }
  
  .editor-actions .btn:hover {
    background: #495057;
  }
}
