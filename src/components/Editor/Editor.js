// src/components/Editor/Editor.js
import React, { useState, useRef, useEffect, useCallback } from 'react';
import Toolbar from '../Toolbar/Toolbar';
import GlossaryPopup from '../GlossaryPopup/GlossaryPopup';
import PasswordModal from '../PasswordModal/PasswordModal';
import AIInsights from '../AIInsights/AIInsights';
import GrammarChecker from '../GrammarChecker/GrammarChecker';
import { updateNote, encryptNote, decryptNote } from '../../utils/storage';
import { identifyGlossaryTerms, checkGrammar } from '../../utils/aiServices';
import './Editor.css';

const Editor = ({ note, onSave }) => {
  const [content, setContent] = useState('');
  const [title, setTitle] = useState('');
  const [isEncrypted, setIsEncrypted] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [glossaryTerms, setGlossaryTerms] = useState([]);
  const [grammarIssues, setGrammarIssues] = useState([]);
  const [selectedGlossaryTerm, setSelectedGlossaryTerm] = useState(null);
  const [showAIInsights, setShowAIInsights] = useState(false);
  const [autoSaveTimeout, setAutoSaveTimeout] = useState(null);

  const editorRef = useRef(null);
  const titleRef = useRef(null);

  // Load note content
  useEffect(() => {
    if (note) {
      setTitle(note.title || '');
      setIsEncrypted(note.encrypted || false);

      if (note.encrypted && note.content) {
        // Show password modal for encrypted notes
        setShowPasswordModal(true);
      } else {
        setContent(note.content || '');
      }
    } else {
      setTitle('');
      setContent('');
      setIsEncrypted(false);
    }
  }, [note]);

  // Auto-save functionality
  const autoSave = useCallback(() => {
    if (note && (content !== note.content || title !== note.title)) {
      const updatedNote = updateNote(note, {
        title: title || 'Untitled Note',
        content,
        encrypted: isEncrypted
      });
      onSave(updatedNote);
    }
  }, [note, content, title, isEncrypted, onSave]);

  // Set up auto-save timer
  useEffect(() => {
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout);
    }

    const timeout = setTimeout(autoSave, 1000); // Auto-save after 1 second of inactivity
    setAutoSaveTimeout(timeout);

    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [content, title, autoSave]);

  // Analyze content for glossary terms and grammar
  useEffect(() => {
    if (content) {
      const terms = identifyGlossaryTerms(content);
      setGlossaryTerms(terms);

      const issues = checkGrammar(content);
      setGrammarIssues(issues);
    } else {
      setGlossaryTerms([]);
      setGrammarIssues([]);
    }
  }, [content]);

  const handlePasswordSubmit = (password, action) => {
    if (action === 'decrypt' && note.content) {
      const decryptedContent = decryptNote(note.content, password);
      if (decryptedContent) {
        setContent(decryptedContent);
        setShowPasswordModal(false);
      } else {
        alert('Incorrect password');
      }
    } else if (action === 'encrypt') {
      const encryptedContent = encryptNote(content, password);
      if (encryptedContent) {
        const updatedNote = updateNote(note, {
          title: title || 'Untitled Note',
          content: encryptedContent,
          encrypted: true
        });
        onSave(updatedNote);
        setIsEncrypted(true);
        setShowPasswordModal(false);
      } else {
        alert('Encryption failed');
      }
    }
  };

  const handleEncryptToggle = () => {
    if (isEncrypted) {
      // Decrypt note
      setShowPasswordModal(true);
    } else {
      // Encrypt note
      setShowPasswordModal(true);
    }
  };

  const handleTitleChange = (e) => {
    setTitle(e.target.value);
  };

  const handleContentChange = (e) => {
    setContent(e.target.value);
  };

  const handleFormatting = (command, value = null) => {
    if (!editorRef.current) return;

    editorRef.current.focus();

    try {
      document.execCommand(command, false, value);
    } catch (error) {
      console.error('Formatting command failed:', error);
    }
  };

  const handleGlossaryTermHover = (term, event) => {
    setSelectedGlossaryTerm({
      ...term,
      x: event.clientX,
      y: event.clientY
    });
  };

  const handleGlossaryTermLeave = () => {
    setSelectedGlossaryTerm(null);
  };

  if (!note) {
    return (
      <div className="editor-container">
        <div className="editor-empty-state">
          <h3>Select a note to start editing</h3>
          <p>Choose a note from the list or create a new one to begin.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="editor-container">
      <div className="editor-header">
        <input
          ref={titleRef}
          type="text"
          value={title}
          onChange={handleTitleChange}
          placeholder="Note title..."
          className="editor-title"
        />
        <div className="editor-actions">
          <button
            className={`btn ${isEncrypted ? 'btn-warning' : 'btn-secondary'}`}
            onClick={handleEncryptToggle}
            title={isEncrypted ? 'Decrypt note' : 'Encrypt note'}
          >
            {isEncrypted ? '🔓' : '🔒'}
          </button>
          <button
            className="btn btn-info"
            onClick={() => setShowAIInsights(!showAIInsights)}
            title="Toggle AI insights"
          >
            🤖
          </button>
        </div>
      </div>

      <Toolbar onFormat={handleFormatting} />

      <div className="editor-content">
        <div className="editor-main">
          <div
            ref={editorRef}
            className="editor-textarea"
            contentEditable
            suppressContentEditableWarning
            onInput={handleContentChange}
            onMouseOver={(e) => {
              const target = e.target;
              if (target.classList.contains('glossary-term')) {
                const termData = glossaryTerms.find(term =>
                  term.start <= target.dataset.start && term.end >= target.dataset.end
                );
                if (termData) {
                  handleGlossaryTermHover(termData, e);
                }
              }
            }}
            onMouseLeave={handleGlossaryTermLeave}
            dangerouslySetInnerHTML={{ __html: content }}
          />

          <GrammarChecker issues={grammarIssues} />
        </div>

        {showAIInsights && (
          <AIInsights
            content={content}
            onClose={() => setShowAIInsights(false)}
          />
        )}
      </div>

      {selectedGlossaryTerm && (
        <GlossaryPopup
          term={selectedGlossaryTerm}
          onClose={handleGlossaryTermLeave}
        />
      )}

      {showPasswordModal && (
        <PasswordModal
          isEncrypted={isEncrypted}
          onSubmit={handlePasswordSubmit}
          onClose={() => setShowPasswordModal(false)}
        />
      )}
    </div>
  );
};

export default Editor;