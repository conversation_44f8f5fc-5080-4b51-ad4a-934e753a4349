// src/components/PasswordModal/PasswordModal.js
import React, { useState, useRef, useEffect } from 'react';
import { FaLock, FaEye, FaEyeSlash, FaTimes } from 'react-icons/fa';
import './PasswordModal.css';

const PasswordModal = ({ isEncrypted, onSubmit, onClose }) => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const passwordInputRef = useRef(null);
  const modalRef = useRef(null);

  useEffect(() => {
    // Focus on password input when modal opens
    if (passwordInputRef.current) {
      passwordInputRef.current.focus();
    }

    // Handle escape key
    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      if (!password.trim()) {
        setError('Password is required');
        return;
      }

      if (!isEncrypted) {
        // Encrypting - need confirmation
        if (password !== confirmPassword) {
          setError('Passwords do not match');
          return;
        }
        
        if (password.length < 6) {
          setError('Password must be at least 6 characters long');
          return;
        }
      }

      const action = isEncrypted ? 'decrypt' : 'encrypt';
      await onSubmit(password, action);
    } catch (error) {
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="password-modal-backdrop" onClick={handleBackdropClick}>
      <div className="password-modal" ref={modalRef}>
        <div className="password-modal-header">
          <div className="modal-title">
            <FaLock className="lock-icon" />
            <h3>
              {isEncrypted ? 'Enter Password to Decrypt' : 'Set Password to Encrypt'}
            </h3>
          </div>
          <button
            className="modal-close-btn"
            onClick={onClose}
            aria-label="Close modal"
          >
            <FaTimes />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="password-form">
          <div className="form-group">
            <label htmlFor="password" className="form-label">
              {isEncrypted ? 'Password' : 'New Password'}
            </label>
            <div className="password-input-container">
              <input
                ref={passwordInputRef}
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="password-input"
                placeholder={isEncrypted ? 'Enter password' : 'Create a strong password'}
                disabled={isLoading}
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
          </div>

          {!isEncrypted && (
            <div className="form-group">
              <label htmlFor="confirmPassword" className="form-label">
                Confirm Password
              </label>
              <div className="password-input-container">
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  id="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="password-input"
                  placeholder="Confirm your password"
                  disabled={isLoading}
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                >
                  {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
            </div>
          )}

          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          {!isEncrypted && (
            <div className="password-requirements">
              <h4>Password Requirements:</h4>
              <ul>
                <li className={password.length >= 6 ? 'requirement-met' : ''}>
                  At least 6 characters long
                </li>
                <li className={/[A-Z]/.test(password) ? 'requirement-met' : ''}>
                  Contains uppercase letter (recommended)
                </li>
                <li className={/[0-9]/.test(password) ? 'requirement-met' : ''}>
                  Contains number (recommended)
                </li>
                <li className={/[^A-Za-z0-9]/.test(password) ? 'requirement-met' : ''}>
                  Contains special character (recommended)
                </li>
              </ul>
            </div>
          )}

          <div className="modal-actions">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={isLoading || !password.trim()}
            >
              {isLoading ? 'Processing...' : (isEncrypted ? 'Decrypt' : 'Encrypt')}
            </button>
          </div>
        </form>

        {isEncrypted && (
          <div className="modal-footer">
            <p className="security-note">
              <strong>Security Note:</strong> If you forget your password, 
              the encrypted content cannot be recovered.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PasswordModal;
