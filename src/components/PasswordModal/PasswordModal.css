/* src/components/PasswordModal/PasswordModal.css */
.password-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.password-modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 450px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
  margin: 1rem;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.password-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.lock-icon {
  color: #ffc107;
  font-size: 1.25rem;
}

.modal-title h3 {
  margin: 0;
  font-size: 1.125rem;
  color: #333;
}

.modal-close-btn {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close-btn:hover {
  color: #495057;
  background: rgba(0, 0, 0, 0.05);
}

.password-form {
  padding: 1rem 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
  font-size: 0.875rem;
}

.password-input-container {
  position: relative;
}

.password-input {
  width: 100%;
  padding: 0.75rem 3rem 0.75rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.875rem;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  box-sizing: border-box;
}

.password-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.password-input:disabled {
  background: #f8f9fa;
  color: #6c757d;
}

.password-toggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 2px;
  transition: color 0.2s ease;
}

.password-toggle:hover {
  color: #495057;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  border: 1px solid #f5c6cb;
}

.password-requirements {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
}

.password-requirements h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  color: #495057;
}

.password-requirements ul {
  margin: 0;
  padding-left: 1.25rem;
  font-size: 0.8125rem;
  color: #6c757d;
}

.password-requirements li {
  margin-bottom: 0.25rem;
  transition: color 0.2s ease;
}

.password-requirements li.requirement-met {
  color: #28a745;
}

.modal-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  text-decoration: none;
  display: inline-block;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
  border-color: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
  border-color: #6c757d;
}

.btn-secondary:hover:not(:disabled) {
  background: #545b62;
  border-color: #545b62;
}

.modal-footer {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.security-note {
  margin: 0;
  font-size: 0.8125rem;
  color: #6c757d;
  line-height: 1.4;
}

/* Responsive design */
@media (max-width: 768px) {
  .password-modal {
    margin: 0.5rem;
    max-width: none;
  }
  
  .password-modal-header {
    padding: 1rem;
  }
  
  .modal-title h3 {
    font-size: 1rem;
  }
  
  .password-form {
    padding: 1rem;
  }
  
  .modal-actions {
    flex-direction: column-reverse;
  }
  
  .btn {
    width: 100%;
  }
  
  .modal-footer {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .password-modal {
    margin: 0.25rem;
  }
  
  .password-modal-header {
    padding: 0.75rem;
  }
  
  .password-form {
    padding: 0.75rem;
  }
  
  .modal-footer {
    padding: 0.75rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .password-modal {
    background: #2d2d2d;
    color: #e9ecef;
  }
  
  .password-modal-header {
    border-color: #404040;
  }
  
  .modal-title h3 {
    color: #e9ecef;
  }
  
  .modal-close-btn {
    color: #adb5bd;
  }
  
  .modal-close-btn:hover {
    color: #e9ecef;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .form-label {
    color: #e9ecef;
  }
  
  .password-input {
    background: #404040;
    border-color: #6c757d;
    color: #e9ecef;
  }
  
  .password-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
  
  .password-input:disabled {
    background: #495057;
    color: #adb5bd;
  }
  
  .password-toggle {
    color: #adb5bd;
  }
  
  .password-toggle:hover {
    color: #e9ecef;
  }
  
  .error-message {
    background: #721c24;
    color: #f8d7da;
    border-color: #a94442;
  }
  
  .password-requirements {
    background: #404040;
  }
  
  .password-requirements h4 {
    color: #ced4da;
  }
  
  .password-requirements ul {
    color: #adb5bd;
  }
  
  .modal-footer {
    background: #404040;
    border-color: #6c757d;
  }
  
  .security-note {
    color: #adb5bd;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .password-modal-backdrop {
    animation: none;
  }
  
  .password-modal {
    animation: none;
  }
  
  .password-input,
  .password-toggle,
  .modal-close-btn,
  .btn {
    transition: none;
  }
}
